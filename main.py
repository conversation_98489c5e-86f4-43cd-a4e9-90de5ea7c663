#!/usr/bin/env python3
"""
RealDebrid qBittorrent API Proxy

A Python application that acts as a qBittorrent Web API server for Sonarr,
but instead of downloading torrents directly, it offloads all downloads to
RealDebrid and exposes finished content via an rclone WEBDAV mount.
"""

import asyncio
import sys
from pathlib import Path

import uvicorn
from loguru import logger

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from linker.config import load_config
from linker.app import create_app


async def main():
    """Main application entry point."""
    # Load configuration
    config = load_config()

    # Setup logging
    logger.remove()  # Remove default handler
    logger.add(
        sys.stderr,
        level=config.logging.format,
        format=config.logging.format,
    )

    # Create logs directory if it doesn't exist
    log_path = Path(config.logging.file)
    log_path.parent.mkdir(parents=True, exist_ok=True)

    # Add file logging
    logger.add(
        config.logging.file,
        level=config.server.log_level,
        format=config.logging.format,
        rotation=config.logging.rotation,
        retention=config.logging.retention,
    )

    # Create data directory if it doesn't exist
    data_path = Path(config.storage.data_dir)
    data_path.mkdir(parents=True, exist_ok=True)

    logger.info("Starting RealDebrid qBittorrent API Proxy")
    logger.info(f"Server will run on {config.server.host}:{config.server.port}")

    # Create FastAPI app
    app = create_app(config)

    # Run the server
    uvicorn_config = uvicorn.Config(
        app,
        host=config.server.host,
        port=config.server.port,
        log_level=config.server.log_level.lower(),
        access_log=True,
    )

    server = uvicorn.Server(uvicorn_config)
    await server.serve()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Shutting down...")
    except Exception as e:
        logger.error(f"Application error: {e}")
        sys.exit(1)