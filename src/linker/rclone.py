"""rclone remote control client."""

import os
from pathlib import Path
from typing import Dict, List, Optional, Any
import httpx
from loguru import logger

from .config import RcloneConfig


class RcloneError(Exception):
    """rclone operation error."""
    pass


class RcloneClient:
    """rclone remote control client."""

    def __init__(self, config: RcloneConfig):
        self.config = config
        self.client: Optional[httpx.AsyncClient] = None

    async def _get_client(self) -> httpx.AsyncClient:
        """Get or create HTTP client."""
        if self.client is None:
            self.client = httpx.AsyncClient(
                base_url=self.config.rc_url,
                timeout=30.0
            )
        return self.client

    async def close(self):
        """Close HTTP client."""
        if self.client:
            await self.client.aclose()
            self.client = None

    async def _make_request(
        self,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Make rclone RC API request."""
        client = await self._get_client()

        try:
            response = await client.post(endpoint, json=data or {})

            if response.status_code >= 400:
                error_msg = f"rclone RC error: HTTP {response.status_code}"
                try:
                    error_data = response.json()
                    if "error" in error_data:
                        error_msg = f"rclone RC error: {error_data['error']}"
                except:
                    pass
                raise RcloneError(error_msg)

            return response.json()

        except httpx.RequestError as e:
            logger.error(f"rclone RC request error: {e}")
            raise RcloneError(f"rclone RC request failed: {e}")
        except Exception as e:
            if isinstance(e, RcloneError):
                raise
            logger.error(f"Unexpected error in rclone RC request: {e}")
            raise RcloneError(f"Unexpected rclone RC error: {e}")

    async def refresh_vfs(self, path: str = "") -> bool:
        """Refresh VFS cache for a specific path or entire mount."""
        try:
            data = {
                "fs": self.config.remote_name,
                "dir": path
            }

            await self._make_request(f"/{self.config.refresh_command}", data)
            logger.info(f"VFS cache refreshed for path: {path or 'root'}")
            return True

        except RcloneError as e:
            logger.error(f"Failed to refresh VFS cache: {e}")
            return False

    async def check_file_exists(self, file_path: str) -> bool:
        """Check if a file exists on the mount."""
        try:
            # First try rclone RC API
            data = {
                "fs": self.config.remote_name,
                "remote": file_path
            }

            result = await self._make_request("/operations/stat", data)
            return result is not None

        except RcloneError:
            # Fallback to filesystem check if RC fails
            try:
                full_path = Path(self.config.mount_path) / file_path.lstrip("/")
                exists = full_path.exists()
                logger.debug(f"File exists check (filesystem): {file_path} -> {exists}")
                return exists
            except Exception as e:
                logger.error(f"Failed to check file existence: {e}")
                return False

    async def list_directory(self, dir_path: str = "") -> List[Dict[str, Any]]:
        """List directory contents."""
        try:
            data = {
                "fs": self.config.remote_name,
                "remote": dir_path
            }

            result = await self._make_request("/operations/list", data)
            return result.get("list", [])

        except RcloneError as e:
            logger.error(f"Failed to list directory {dir_path}: {e}")
            return []

    async def get_file_info(self, file_path: str) -> Optional[Dict[str, Any]]:
        """Get file information."""
        try:
            data = {
                "fs": self.config.remote_name,
                "remote": file_path
            }

            result = await self._make_request("/operations/stat", data)
            return result

        except RcloneError as e:
            logger.debug(f"File not found or error getting info for {file_path}: {e}")
            return None

    async def find_torrent_files(self, torrent_name: str) -> List[str]:
        """Find files related to a torrent by name."""
        try:
            # Search for files/directories matching the torrent name
            files = []

            # Try to list root directory
            root_files = await self.list_directory("")

            for item in root_files:
                item_name = item.get("Name", "")
                if torrent_name.lower() in item_name.lower():
                    files.append(item_name)

                    # If it's a directory, also check if files exist inside
                    if item.get("IsDir", False):
                        subfiles = await self.list_directory(item_name)
                        for subfile in subfiles:
                            if not subfile.get("IsDir", False):
                                files.append(f"{item_name}/{subfile.get('Name', '')}")

            logger.debug(f"Found {len(files)} files for torrent '{torrent_name}': {files}")
            return files

        except Exception as e:
            logger.error(f"Error finding torrent files for '{torrent_name}': {e}")
            return []

    async def health_check(self) -> bool:
        """Check if rclone RC is accessible."""
        try:
            await self._make_request("/core/version")
            return True
        except Exception as e:
            logger.warning(f"rclone RC health check failed: {e}")
            return False