"""Data models for the application."""

from datetime import datetime
from enum import Enum
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


class TorrentState(str, Enum):
    """Torrent states that match qBittorrent states."""
    ERROR = "error"
    MISSING_FILES = "missingFiles"
    UPLOADING = "uploading"
    PAUSED_UP = "pausedUP"
    QUEUED_UP = "queuedUP"
    STALLED_UP = "stalledUP"
    CHECKING_UP = "checkingUP"
    FORCED_UP = "forcedUP"
    ALLOCATING = "allocating"
    DOWNLOADING = "downloading"
    METADATA_DL = "metaDL"
    PAUSED_DL = "pausedDL"
    QUEUED_DL = "queuedDL"
    STALLED_DL = "stalledDL"
    CHECKING_DL = "checkingDL"
    FORCED_DL = "forcedDL"
    CHECKING_RESUME_DATA = "checkingResumeData"
    MOVING = "moving"
    UNKNOWN = "unknown"


class RealDebridTorrentStatus(str, Enum):
    """RealDebrid torrent statuses."""
    MAGNET_ERROR = "magnet_error"
    MAGNET_CONVERSION = "magnet_conversion"
    WAITING_FILES_SELECTION = "waiting_files_selection"
    QUEUED = "queued"
    DOWNLOADING = "downloading"
    DOWNLOADED = "downloaded"
    ERROR = "error"
    VIRUS = "virus"
    COMPRESSING = "compressing"
    UPLOADING = "uploading"
    DEAD = "dead"


class TorrentInfo(BaseModel):
    """Torrent information model that matches qBittorrent API format."""
    added_on: int = Field(description="Time when torrent was added to client")
    amount_left: int = Field(description="Amount of data left to download (bytes)")
    auto_tmm: bool = Field(description="Whether this torrent is managed by Automatic Torrent Management")
    availability: float = Field(description="Percentage of file pieces currently available")
    category: str = Field(description="Category of the torrent")
    completed: int = Field(description="Amount of transfer data completed (bytes)")
    completion_on: int = Field(description="Time when torrent completed")
    content_path: str = Field(description="Absolute path of torrent content (root path for multifile torrents)")
    dl_limit: int = Field(description="Torrent download speed limit (bytes/s)")
    dlspeed: int = Field(description="Torrent download speed (bytes/s)")
    downloaded: int = Field(description="Amount of data downloaded")
    downloaded_session: int = Field(description="Amount of data downloaded this session")
    eta: int = Field(description="Torrent ETA (seconds)")
    f_l_piece_prio: bool = Field(description="True if first last piece are prioritized")
    force_start: bool = Field(description="True if force start is enabled for this torrent")
    hash: str = Field(description="Torrent hash")
    infohash_v1: str = Field(description="Torrent info hash v1")
    infohash_v2: str = Field(description="Torrent info hash v2")
    last_activity: int = Field(description="Last time when a chunk was downloaded/uploaded")
    magnet_uri: str = Field(description="Magnet URI corresponding to this torrent")
    max_ratio: float = Field(description="Maximum share ratio until torrent is stopped from seeding/uploading")
    max_seeding_time: int = Field(description="Maximum seeding time until torrent is stopped from seeding")
    name: str = Field(description="Torrent name")
    num_complete: int = Field(description="Number of seeds in the swarm")
    num_incomplete: int = Field(description="Number of leechers in the swarm")
    num_leechs: int = Field(description="Number of leechers connected to")
    num_seeds: int = Field(description="Number of seeds connected to")
    priority: int = Field(description="Torrent priority")
    progress: float = Field(description="Torrent progress (percentage/100)")
    ratio: float = Field(description="Torrent share ratio")
    ratio_limit: float = Field(description="TODO (what is different from max_ratio?)")
    save_path: str = Field(description="Path where this torrent's data is stored")
    seeding_time: int = Field(description="Torrent elapsed time while complete (seconds)")
    seeding_time_limit: int = Field(description="TODO (what is different from max_seeding_time?)")
    seen_complete: int = Field(description="Time when this torrent was last seen complete")
    seq_dl: bool = Field(description="True if sequential download is enabled")
    size: int = Field(description="Total size (bytes) of files selected for download")
    state: TorrentState = Field(description="Torrent state")
    super_seeding: bool = Field(description="True if super seeding is enabled")
    tags: str = Field(description="Comma-concatenated tag list of the torrent")
    time_active: int = Field(description="Total active time (seconds)")
    total_size: int = Field(description="Total size (bytes) of all file in this torrent")
    tracker: str = Field(description="The first tracker with working status")
    trackers_count: int = Field(description="Number of trackers for this torrent")
    up_limit: int = Field(description="Torrent upload speed limit (bytes/s)")
    uploaded: int = Field(description="Amount of data uploaded")
    uploaded_session: int = Field(description="Amount of data uploaded this session")
    upspeed: int = Field(description="Torrent upload speed (bytes/s)")


class TorrentData(BaseModel):
    """Internal torrent data model."""
    hash: str
    name: str
    magnet_uri: Optional[str] = None
    realdebrid_id: Optional[str] = None
    realdebrid_status: Optional[RealDebridTorrentStatus] = None
    state: TorrentState = TorrentState.UNKNOWN
    category: str = ""
    tags: str = ""
    added_on: datetime
    size: int = 0
    progress: float = 0.0
    completed: int = 0
    downloaded: int = 0
    uploaded: int = 0
    dlspeed: int = 0
    upspeed: int = 0
    eta: int = 8640000  # Large number for unknown ETA
    priority: int = 1
    paused: bool = False
    save_path: str = ""
    content_path: str = ""
    availability: float = -1.0
    ratio: float = 0.0
    num_seeds: int = 0
    num_leechs: int = 0
    time_active: int = 0
    seeding_time: int = 0
    last_activity: int = 0
    completion_on: int = 0
    tracker: str = ""

    def to_qbittorrent_format(self) -> Dict[str, Any]:
        """Convert to qBittorrent API format."""
        return {
            "added_on": int(self.added_on.timestamp()),
            "amount_left": max(0, self.size - self.completed),
            "auto_tmm": False,
            "availability": self.availability,
            "category": self.category,
            "completed": self.completed,
            "completion_on": self.completion_on,
            "content_path": self.content_path,
            "dl_limit": -1,
            "dlspeed": self.dlspeed,
            "downloaded": self.downloaded,
            "downloaded_session": self.downloaded,
            "eta": self.eta,
            "f_l_piece_prio": False,
            "force_start": False,
            "hash": self.hash,
            "infohash_v1": self.hash,
            "infohash_v2": "",
            "last_activity": self.last_activity,
            "magnet_uri": self.magnet_uri or "",
            "max_ratio": -1,
            "max_seeding_time": -1,
            "name": self.name,
            "num_complete": self.num_seeds,
            "num_incomplete": self.num_leechs,
            "num_leechs": self.num_leechs,
            "num_seeds": self.num_seeds,
            "priority": self.priority,
            "progress": self.progress,
            "ratio": self.ratio,
            "ratio_limit": -2,
            "save_path": self.save_path,
            "seeding_time": self.seeding_time,
            "seeding_time_limit": -2,
            "seen_complete": self.completion_on,
            "seq_dl": False,
            "size": self.size,
            "state": self.state.value,
            "super_seeding": False,
            "tags": self.tags,
            "time_active": self.time_active,
            "total_size": self.size,
            "tracker": self.tracker,
            "trackers_count": 1 if self.tracker else 0,
            "up_limit": -1,
            "uploaded": self.uploaded,
            "uploaded_session": self.uploaded,
            "upspeed": self.upspeed,
        }