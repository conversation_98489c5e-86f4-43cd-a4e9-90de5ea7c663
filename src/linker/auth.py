"""Authentication management for qBittorrent API emulation."""

import secrets
import time
from typing import Dict, Optional

from fastapi import HTT<PERSON>Ex<PERSON>, status, <PERSON>ie, Depends
from loguru import logger

from .config import QBittorrentConfig


class AuthManager:
    """Manages authentication sessions for qBittorrent API emulation."""

    def __init__(self, config: QBittorrentConfig):
        self.config = config
        self.sessions: Dict[str, float] = {}  # session_id -> expiry_time

    def authenticate(self, username: str, password: str) -> str:
        """Authenticate user and return session ID."""
        if username != self.config.username or password != self.config.password:
            logger.warning(f"Failed login attempt for user: {username}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Invalid credentials"
            )

        # Generate session ID
        session_id = secrets.token_urlsafe(32)
        expiry_time = time.time() + self.config.session_timeout

        self.sessions[session_id] = expiry_time

        logger.info(f"User {username} authenticated successfully")
        return session_id

    def validate_session(self, session_id: Optional[str]) -> bool:
        """Validate session ID."""
        if not session_id:
            return False

        if session_id not in self.sessions:
            return False

        # Check if session has expired
        if time.time() > self.sessions[session_id]:
            del self.sessions[session_id]
            return False

        return True

    def logout(self, session_id: Optional[str]) -> None:
        """Logout user by removing session."""
        if session_id and session_id in self.sessions:
            del self.sessions[session_id]
            logger.info("User logged out successfully")

    def cleanup_expired_sessions(self) -> None:
        """Remove expired sessions."""
        current_time = time.time()
        expired_sessions = [
            sid for sid, expiry in self.sessions.items()
            if current_time > expiry
        ]

        for sid in expired_sessions:
            del self.sessions[sid]

        if expired_sessions:
            logger.debug(f"Cleaned up {len(expired_sessions)} expired sessions")


def get_session_id(SID: Optional[str] = Cookie(None)) -> Optional[str]:
    """Extract session ID from cookie."""
    return SID


def require_auth(
    session_id: Optional[str] = Depends(get_session_id),
    auth_manager: AuthManager = None  # Will be injected by dependency
) -> str:
    """Dependency that requires valid authentication."""
    if not auth_manager.validate_session(session_id):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required"
        )
    return session_id