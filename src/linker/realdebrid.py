"""RealDebrid API client."""

import asyncio
from typing import Dict, List, Optional, Any, Union
import httpx
from loguru import logger
from asyncio_throttle import Throttler

from .config import RealDebridConfig
from .models import RealDebridTorrentStatus


class RealDebridError(Exception):
    """RealDebrid API error."""

    def __init__(self, message: str, status_code: Optional[int] = None, error_code: Optional[int] = None):
        super().__init__(message)
        self.status_code = status_code
        self.error_code = error_code


class RealDebridClient:
    """RealDebrid API client."""

    def __init__(self, config: RealDebridConfig):
        self.config = config
        self.client: Optional[httpx.AsyncClient] = None
        # Rate limiting: 250 requests per minute
        self.throttler = Throttler(rate_limit=250, period=60)

    async def _get_client(self) -> httpx.AsyncClient:
        """Get or create HTTP client."""
        if self.client is None:
            self.client = httpx.AsyncClient(
                base_url=self.config.base_url,
                headers={
                    "Authorization": f"Bearer {self.config.api_token}",
                    "User-Agent": "RealDebrid-qBittorrent-Proxy/1.0"
                },
                timeout=30.0
            )
        return self.client

    async def close(self):
        """Close HTTP client."""
        if self.client:
            await self.client.aclose()
            self.client = None

    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        files: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Make API request with rate limiting and error handling."""
        async with self.throttler:
            client = await self._get_client()

            try:
                if method.upper() == "GET":
                    response = await client.get(endpoint, params=params)
                elif method.upper() == "POST":
                    response = await client.post(endpoint, data=data, files=files, params=params)
                elif method.upper() == "PUT":
                    response = await client.put(endpoint, data=data, files=files, params=params)
                elif method.upper() == "DELETE":
                    response = await client.delete(endpoint, params=params)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")

                # Handle different response types
                if response.status_code == 204:
                    return {}

                if response.headers.get("content-type", "").startswith("application/json"):
                    result = response.json()
                else:
                    result = {"data": response.text}

                if response.status_code >= 400:
                    error_msg = result.get("error", f"HTTP {response.status_code}")
                    error_code = result.get("error_code")
                    raise RealDebridError(error_msg, response.status_code, error_code)

                return result

            except httpx.RequestError as e:
                logger.error(f"RealDebrid request error: {e}")
                raise RealDebridError(f"Request failed: {e}")
            except Exception as e:
                if isinstance(e, RealDebridError):
                    raise
                logger.error(f"Unexpected error in RealDebrid request: {e}")
                raise RealDebridError(f"Unexpected error: {e}")

    async def get_user_info(self) -> Dict[str, Any]:
        """Get current user information."""
        return await self._make_request("GET", "/user")

    async def get_available_hosts(self) -> List[str]:
        """Get available hosts for torrent uploads."""
        result = await self._make_request("GET", "/torrents/availableHosts")
        return result

    async def add_magnet(self, magnet_link: str, host: Optional[str] = None) -> Dict[str, Any]:
        """Add magnet link to RealDebrid."""
        data = {"magnet": magnet_link}
        if host:
            data["host"] = host
        elif self.config.preferred_host:
            data["host"] = self.config.preferred_host

        logger.info(f"Adding magnet to RealDebrid: {magnet_link[:50]}...")
        return await self._make_request("POST", "/torrents/addMagnet", data=data)

    async def add_torrent_file(self, torrent_data: bytes, host: Optional[str] = None) -> Dict[str, Any]:
        """Add torrent file to RealDebrid."""
        params = {}
        if host:
            params["host"] = host
        elif self.config.preferred_host:
            params["host"] = self.config.preferred_host

        files = {"torrent": ("torrent.torrent", torrent_data, "application/x-bittorrent")}

        logger.info("Adding torrent file to RealDebrid")
        return await self._make_request("PUT", "/torrents/addTorrent", files=files, params=params)

    async def get_torrents(self, active_only: bool = False) -> List[Dict[str, Any]]:
        """Get list of torrents."""
        params = {}
        if active_only:
            params["filter"] = "active"

        return await self._make_request("GET", "/torrents", params=params)

    async def get_torrent_info(self, torrent_id: str) -> Dict[str, Any]:
        """Get detailed information about a torrent."""
        return await self._make_request("GET", f"/torrents/info/{torrent_id}")

    async def select_files(self, torrent_id: str, file_ids: Union[str, List[int]]) -> None:
        """Select files for a torrent."""
        if isinstance(file_ids, list):
            file_ids_str = ",".join(map(str, file_ids))
        else:
            file_ids_str = file_ids

        data = {"files": file_ids_str}
        await self._make_request("POST", f"/torrents/selectFiles/{torrent_id}", data=data)
        logger.info(f"Selected files for torrent {torrent_id}: {file_ids_str}")

    async def delete_torrent(self, torrent_id: str) -> None:
        """Delete a torrent."""
        await self._make_request("DELETE", f"/torrents/delete/{torrent_id}")
        logger.info(f"Deleted torrent {torrent_id}")

    async def get_active_count(self) -> Dict[str, int]:
        """Get currently active torrents count and limit."""
        return await self._make_request("GET", "/torrents/activeCount")