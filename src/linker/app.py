"""FastAPI application for qBittorrent API emulation."""

import asyncio
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger

from .config import Config
from .auth import AuthManager
from .torrent_manager import TorrentManager
from .realdebrid import RealDebridClient
from .rclone import RcloneClient
from .api import auth_router, torrents_router, app_router, sync_router


class AppState:
    """Application state container."""

    def __init__(self, config: Config):
        self.config = config
        self.auth_manager = AuthManager(config.qbittorrent)
        self.realdebrid_client = RealDebridClient(config.realdebrid)
        self.rclone_client = RcloneClient(config.rclone)
        self.torrent_manager = TorrentManager(
            config.storage,
            self.realdebrid_client,
            self.rclone_client
        )
        self._background_task: asyncio.Task = None

    async def start(self):
        """Start background services."""
        logger.info("Starting application services...")

        # Initialize torrent manager
        await self.torrent_manager.initialize()

        # Start background polling task
        self._background_task = asyncio.create_task(self._background_polling())

        logger.info("Application services started")

    async def stop(self):
        """Stop background services."""
        logger.info("Stopping application services...")

        if self._background_task:
            self._background_task.cancel()
            try:
                await self._background_task
            except asyncio.CancelledError:
                pass

        await self.torrent_manager.close()
        await self.realdebrid_client.close()
        await self.rclone_client.close()

        logger.info("Application services stopped")

    async def _background_polling(self):
        """Background task for polling torrent status."""
        while True:
            try:
                await self.torrent_manager.poll_torrents()
                await asyncio.sleep(self.config.realdebrid.poll_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in background polling: {e}")
                await asyncio.sleep(5)  # Short delay before retrying


# Global app state
app_state: AppState = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    global app_state

    # Startup
    await app_state.start()

    yield

    # Shutdown
    await app_state.stop()


def get_app_state() -> AppState:
    """Dependency to get application state."""
    return app_state


def create_app(config: Config) -> FastAPI:
    """Create and configure FastAPI application."""
    global app_state
    app_state = AppState(config)

    app = FastAPI(
        title="RealDebrid qBittorrent API Proxy",
        description="A proxy that emulates qBittorrent Web API for Sonarr using RealDebrid",
        version="1.0.0",
        lifespan=lifespan,
    )

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Include routers
    app.include_router(auth_router, prefix="/api/v2/auth", tags=["auth"])
    app.include_router(app_router, prefix="/api/v2/app", tags=["app"])
    app.include_router(torrents_router, prefix="/api/v2/torrents", tags=["torrents"])
    app.include_router(sync_router, prefix="/api/v2/sync", tags=["sync"])

    @app.get("/")
    async def root():
        """Root endpoint."""
        return {
            "message": "RealDebrid qBittorrent API Proxy",
            "version": "1.0.0",
            "status": "running"
        }

    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        return {"status": "healthy"}

    return app