"""Torrent management and state tracking."""

import asyncio
import hashlib
import json
import sqlite3
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from urllib.parse import parse_qs, urlparse

from loguru import logger

from .config import StorageConfig
from .models import TorrentData, TorrentState, RealDebridTorrentStatus
from .realdebrid import RealDebridClient, RealDebridError
from .rclone import RcloneClient


class TorrentManager:
    """Manages torrent state and coordinates between RealDebrid and rclone."""

    def __init__(
        self,
        storage_config: StorageConfig,
        realdebrid_client: RealDebridClient,
        rclone_client: RcloneClient
    ):
        self.storage_config = storage_config
        self.realdebrid = realdebrid_client
        self.rclone = rclone_client
        self.db_path = Path(storage_config.data_dir) / storage_config.db_file
        self.torrents: Dict[str, TorrentData] = {}
        self._sync_id = 0
        self._last_sync_data: Dict[str, Any] = {}

    async def initialize(self):
        """Initialize the torrent manager."""
        # Create data directory if it doesn't exist
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        # Initialize database
        await self._init_database()

        # Load existing torrents
        await self._load_torrents()

        logger.info(f"TorrentManager initialized with {len(self.torrents)} torrents")

    async def close(self):
        """Close the torrent manager."""
        # Save current state
        await self._save_torrents()
        logger.info("TorrentManager closed")

    def _get_db_connection(self) -> sqlite3.Connection:
        """Get database connection."""
        conn = sqlite3.connect(str(self.db_path))
        conn.row_factory = sqlite3.Row
        return conn

    async def _init_database(self):
        """Initialize SQLite database."""
        conn = self._get_db_connection()
        try:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS torrents (
                    hash TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    magnet_uri TEXT,
                    realdebrid_id TEXT,
                    realdebrid_status TEXT,
                    state TEXT NOT NULL,
                    category TEXT DEFAULT '',
                    tags TEXT DEFAULT '',
                    added_on INTEGER NOT NULL,
                    size INTEGER DEFAULT 0,
                    progress REAL DEFAULT 0.0,
                    completed INTEGER DEFAULT 0,
                    downloaded INTEGER DEFAULT 0,
                    uploaded INTEGER DEFAULT 0,
                    dlspeed INTEGER DEFAULT 0,
                    upspeed INTEGER DEFAULT 0,
                    eta INTEGER DEFAULT 8640000,
                    priority INTEGER DEFAULT 1,
                    paused INTEGER DEFAULT 0,
                    save_path TEXT DEFAULT '',
                    content_path TEXT DEFAULT '',
                    availability REAL DEFAULT -1.0,
                    ratio REAL DEFAULT 0.0,
                    num_seeds INTEGER DEFAULT 0,
                    num_leechs INTEGER DEFAULT 0,
                    time_active INTEGER DEFAULT 0,
                    seeding_time INTEGER DEFAULT 0,
                    last_activity INTEGER DEFAULT 0,
                    completion_on INTEGER DEFAULT 0,
                    tracker TEXT DEFAULT '',
                    updated_at INTEGER NOT NULL
                )
            """)
            conn.commit()
        finally:
            conn.close()

    async def _load_torrents(self):
        """Load torrents from database."""
        conn = self._get_db_connection()
        try:
            cursor = conn.execute("SELECT * FROM torrents")
            for row in cursor.fetchall():
                torrent_data = TorrentData(
                    hash=row["hash"],
                    name=row["name"],
                    magnet_uri=row["magnet_uri"],
                    realdebrid_id=row["realdebrid_id"],
                    realdebrid_status=RealDebridTorrentStatus(row["realdebrid_status"]) if row["realdebrid_status"] else None,
                    state=TorrentState(row["state"]),
                    category=row["category"],
                    tags=row["tags"],
                    added_on=datetime.fromtimestamp(row["added_on"]),
                    size=row["size"],
                    progress=row["progress"],
                    completed=row["completed"],
                    downloaded=row["downloaded"],
                    uploaded=row["uploaded"],
                    dlspeed=row["dlspeed"],
                    upspeed=row["upspeed"],
                    eta=row["eta"],
                    priority=row["priority"],
                    paused=bool(row["paused"]),
                    save_path=row["save_path"],
                    content_path=row["content_path"],
                    availability=row["availability"],
                    ratio=row["ratio"],
                    num_seeds=row["num_seeds"],
                    num_leechs=row["num_leechs"],
                    time_active=row["time_active"],
                    seeding_time=row["seeding_time"],
                    last_activity=row["last_activity"],
                    completion_on=row["completion_on"],
                    tracker=row["tracker"]
                )
                self.torrents[torrent_data.hash] = torrent_data
        finally:
            conn.close()

    async def _save_torrents(self):
        """Save torrents to database."""
        conn = self._get_db_connection()
        try:
            current_time = int(time.time())
            for torrent in self.torrents.values():
                conn.execute("""
                    INSERT OR REPLACE INTO torrents (
                        hash, name, magnet_uri, realdebrid_id, realdebrid_status,
                        state, category, tags, added_on, size, progress, completed,
                        downloaded, uploaded, dlspeed, upspeed, eta, priority,
                        paused, save_path, content_path, availability, ratio,
                        num_seeds, num_leechs, time_active, seeding_time,
                        last_activity, completion_on, tracker, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    torrent.hash, torrent.name, torrent.magnet_uri,
                    torrent.realdebrid_id, torrent.realdebrid_status.value if torrent.realdebrid_status else None,
                    torrent.state.value, torrent.category, torrent.tags,
                    int(torrent.added_on.timestamp()), torrent.size, torrent.progress,
                    torrent.completed, torrent.downloaded, torrent.uploaded,
                    torrent.dlspeed, torrent.upspeed, torrent.eta, torrent.priority,
                    int(torrent.paused), torrent.save_path, torrent.content_path,
                    torrent.availability, torrent.ratio, torrent.num_seeds,
                    torrent.num_leechs, torrent.time_active, torrent.seeding_time,
                    torrent.last_activity, torrent.completion_on, torrent.tracker,
                    current_time
                ))
            conn.commit()
        finally:
            conn.close()

    def _extract_hash_from_magnet(self, magnet_uri: str) -> str:
        """Extract info hash from magnet URI."""
        try:
            parsed = urlparse(magnet_uri)
            if parsed.scheme != "magnet":
                raise ValueError("Not a magnet URI")

            params = parse_qs(parsed.query)
            xt_values = params.get("xt", [])

            for xt in xt_values:
                if xt.startswith("urn:btih:"):
                    hash_value = xt[9:]  # Remove "urn:btih:" prefix
                    # Convert to lowercase and ensure it's 40 characters (SHA-1)
                    if len(hash_value) == 32:  # Base32 encoded
                        # Convert base32 to hex (this is a simplified conversion)
                        # In a real implementation, you'd use proper base32 decoding
                        hash_value = hashlib.sha1(hash_value.encode()).hexdigest()
                    return hash_value.lower()

            raise ValueError("No valid info hash found in magnet URI")

        except Exception as e:
            # Fallback: generate hash from magnet URI
            logger.warning(f"Could not extract hash from magnet URI, generating fallback: {e}")
            return hashlib.sha1(magnet_uri.encode()).hexdigest()

    def _extract_name_from_magnet(self, magnet_uri: str) -> str:
        """Extract display name from magnet URI."""
        try:
            parsed = urlparse(magnet_uri)
            params = parse_qs(parsed.query)
            dn_values = params.get("dn", [])
            if dn_values:
                return dn_values[0]
        except Exception:
            pass

        # Fallback to hash
        return f"Torrent_{self._extract_hash_from_magnet(magnet_uri)[:8]}"

    async def add_magnet(
        self,
        magnet_uri: str,
        category: Optional[str] = None,
        tags: Optional[str] = None,
        paused: bool = False
    ) -> str:
        """Add magnet link and upload to RealDebrid."""
        try:
            # Extract hash and name
            torrent_hash = self._extract_hash_from_magnet(magnet_uri)
            torrent_name = self._extract_name_from_magnet(magnet_uri)

            # Check if torrent already exists
            if torrent_hash in self.torrents:
                logger.warning(f"Torrent already exists: {torrent_hash}")
                return torrent_hash

            # Create torrent data
            torrent_data = TorrentData(
                hash=torrent_hash,
                name=torrent_name,
                magnet_uri=magnet_uri,
                state=TorrentState.METADATA_DL if not paused else TorrentState.PAUSED_DL,
                category=category or "",
                tags=tags or "",
                added_on=datetime.now(),
                paused=paused,
                save_path=self.rclone.config.mount_path,
                content_path=self.rclone.config.mount_path
            )

            # Add to local storage
            self.torrents[torrent_hash] = torrent_data

            # Upload to RealDebrid
            try:
                result = await self.realdebrid.add_magnet(magnet_uri)
                torrent_data.realdebrid_id = result.get("id")
                torrent_data.realdebrid_status = RealDebridTorrentStatus.MAGNET_CONVERSION

                logger.info(f"Added magnet to RealDebrid: {torrent_name} (ID: {torrent_data.realdebrid_id})")

                # Start monitoring this torrent
                asyncio.create_task(self._monitor_torrent(torrent_hash))

            except RealDebridError as e:
                logger.error(f"Failed to add magnet to RealDebrid: {e}")
                torrent_data.state = TorrentState.ERROR

            # Save to database
            await self._save_torrents()

            return torrent_hash

        except Exception as e:
            logger.error(f"Error adding magnet: {e}")
            raise

    async def add_torrent_file(
        self,
        torrent_data: bytes,
        filename: str,
        category: Optional[str] = None,
        tags: Optional[str] = None,
        paused: bool = False
    ) -> str:
        """Add torrent file and upload to RealDebrid."""
        try:
            # Generate hash from torrent data
            torrent_hash = hashlib.sha1(torrent_data).hexdigest()

            # Extract name from filename
            torrent_name = filename.replace(".torrent", "")

            # Check if torrent already exists
            if torrent_hash in self.torrents:
                logger.warning(f"Torrent already exists: {torrent_hash}")
                return torrent_hash

            # Create torrent data
            torrent_data_obj = TorrentData(
                hash=torrent_hash,
                name=torrent_name,
                state=TorrentState.CHECKING_DL if not paused else TorrentState.PAUSED_DL,
                category=category or "",
                tags=tags or "",
                added_on=datetime.now(),
                paused=paused,
                save_path=self.rclone.config.mount_path,
                content_path=self.rclone.config.mount_path
            )

            # Add to local storage
            self.torrents[torrent_hash] = torrent_data_obj

            # Upload to RealDebrid
            try:
                result = await self.realdebrid.add_torrent_file(torrent_data)
                torrent_data_obj.realdebrid_id = result.get("id")
                torrent_data_obj.realdebrid_status = RealDebridTorrentStatus.WAITING_FILES_SELECTION

                logger.info(f"Added torrent file to RealDebrid: {torrent_name} (ID: {torrent_data_obj.realdebrid_id})")

                # Start monitoring this torrent
                asyncio.create_task(self._monitor_torrent(torrent_hash))

            except RealDebridError as e:
                logger.error(f"Failed to add torrent file to RealDebrid: {e}")
                torrent_data_obj.state = TorrentState.ERROR

            # Save to database
            await self._save_torrents()

            return torrent_hash

        except Exception as e:
            logger.error(f"Error adding torrent file: {e}")
            raise

    async def _monitor_torrent(self, torrent_hash: str):
        """Monitor a specific torrent for status changes."""
        torrent = self.torrents.get(torrent_hash)
        if not torrent or not torrent.realdebrid_id:
            return

        try:
            # Get torrent info from RealDebrid
            rd_info = await self.realdebrid.get_torrent_info(torrent.realdebrid_id)

            # Update torrent status
            await self._update_torrent_from_realdebrid(torrent, rd_info)

            # Handle file selection for new torrents
            if torrent.realdebrid_status == RealDebridTorrentStatus.WAITING_FILES_SELECTION:
                await self._select_all_files(torrent.realdebrid_id)

        except Exception as e:
            logger.error(f"Error monitoring torrent {torrent_hash}: {e}")

    async def _select_all_files(self, realdebrid_id: str):
        """Select all files for a torrent."""
        try:
            await self.realdebrid.select_files(realdebrid_id, "all")
            logger.info(f"Selected all files for torrent {realdebrid_id}")
        except Exception as e:
            logger.error(f"Failed to select files for torrent {realdebrid_id}: {e}")

    async def _update_torrent_from_realdebrid(self, torrent: TorrentData, rd_info: Dict[str, Any]):
        """Update torrent data from RealDebrid info."""
        try:
            # Update RealDebrid status
            rd_status = rd_info.get("status", "")
            if rd_status:
                torrent.realdebrid_status = RealDebridTorrentStatus(rd_status)

            # Update basic info
            torrent.name = rd_info.get("filename", torrent.name)
            torrent.size = rd_info.get("bytes", torrent.size)
            torrent.progress = rd_info.get("progress", 0) / 100.0
            torrent.completed = int(torrent.size * torrent.progress)

            # Update speeds and ETA
            if "speed" in rd_info:
                torrent.dlspeed = rd_info["speed"]

            # Update state based on RealDebrid status
            torrent.state = self._map_realdebrid_status_to_state(torrent.realdebrid_status, torrent.paused)

            # Update activity timestamp
            torrent.last_activity = int(time.time())

            # If torrent is downloaded, check file availability
            if torrent.realdebrid_status == RealDebridTorrentStatus.DOWNLOADED:
                await self._handle_completed_torrent(torrent)

        except Exception as e:
            logger.error(f"Error updating torrent from RealDebrid: {e}")

    def _map_realdebrid_status_to_state(self, rd_status: RealDebridTorrentStatus, paused: bool) -> TorrentState:
        """Map RealDebrid status to qBittorrent state."""
        if paused:
            return TorrentState.PAUSED_DL

        mapping = {
            RealDebridTorrentStatus.MAGNET_ERROR: TorrentState.ERROR,
            RealDebridTorrentStatus.MAGNET_CONVERSION: TorrentState.METADATA_DL,
            RealDebridTorrentStatus.WAITING_FILES_SELECTION: TorrentState.PAUSED_DL,
            RealDebridTorrentStatus.QUEUED: TorrentState.QUEUED_DL,
            RealDebridTorrentStatus.DOWNLOADING: TorrentState.DOWNLOADING,
            RealDebridTorrentStatus.DOWNLOADED: TorrentState.UPLOADING,  # Will be changed to completed after file check
            RealDebridTorrentStatus.ERROR: TorrentState.ERROR,
            RealDebridTorrentStatus.VIRUS: TorrentState.ERROR,
            RealDebridTorrentStatus.COMPRESSING: TorrentState.DOWNLOADING,
            RealDebridTorrentStatus.UPLOADING: TorrentState.UPLOADING,
            RealDebridTorrentStatus.DEAD: TorrentState.ERROR,
        }

        return mapping.get(rd_status, TorrentState.UNKNOWN)

    async def _handle_completed_torrent(self, torrent: TorrentData):
        """Handle torrent that has completed downloading on RealDebrid."""
        try:
            # Refresh rclone VFS cache
            await self.rclone.refresh_vfs()

            # Wait a bit for the refresh to take effect
            await asyncio.sleep(2)

            # Check if files are available
            files = await self.rclone.find_torrent_files(torrent.name)

            if files:
                # Files are available, mark as completed
                torrent.state = TorrentState.UPLOADING  # Seeding equivalent
                torrent.progress = 1.0
                torrent.completed = torrent.size
                torrent.completion_on = int(time.time())

                # Update content path to the first found file/directory
                if files:
                    torrent.content_path = str(Path(self.rclone.config.mount_path) / files[0])

                logger.info(f"Torrent completed and files available: {torrent.name}")
            else:
                # Files not yet available, keep checking
                logger.warning(f"Torrent completed but files not yet available: {torrent.name}")

        except Exception as e:
            logger.error(f"Error handling completed torrent {torrent.name}: {e}")

    async def poll_torrents(self):
        """Poll all active torrents for status updates."""
        try:
            # Get active torrents from RealDebrid
            rd_torrents = await self.realdebrid.get_torrents(active_only=True)
            rd_torrents_by_id = {t["id"]: t for t in rd_torrents}

            # Update our torrents with RealDebrid data
            for torrent in self.torrents.values():
                if torrent.realdebrid_id and torrent.realdebrid_id in rd_torrents_by_id:
                    rd_info = rd_torrents_by_id[torrent.realdebrid_id]
                    await self._update_torrent_from_realdebrid(torrent, rd_info)

            # Save updated state
            await self._save_torrents()

        except Exception as e:
            logger.error(f"Error polling torrents: {e}")

    async def get_torrents_info(
        self,
        filter: Optional[str] = None,
        category: Optional[str] = None,
        tag: Optional[str] = None,
        sort: Optional[str] = None,
        reverse: Optional[bool] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        hashes: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get torrents info in qBittorrent format."""
        torrents = list(self.torrents.values())

        # Filter by hashes if specified
        if hashes and hashes != "all":
            hash_list = hashes.split("|")
            torrents = [t for t in torrents if t.hash in hash_list]

        # Filter by category
        if category:
            torrents = [t for t in torrents if t.category == category]

        # Filter by tag
        if tag:
            torrents = [t for t in torrents if tag in t.tags.split(",")]

        # Filter by state
        if filter:
            if filter == "active":
                torrents = [t for t in torrents if t.state in [
                    TorrentState.DOWNLOADING, TorrentState.UPLOADING, TorrentState.METADATA_DL
                ]]
            elif filter == "inactive":
                torrents = [t for t in torrents if t.state in [
                    TorrentState.PAUSED_DL, TorrentState.PAUSED_UP, TorrentState.ERROR
                ]]
            elif filter == "completed":
                torrents = [t for t in torrents if t.progress >= 1.0]
            elif filter == "downloading":
                torrents = [t for t in torrents if t.state == TorrentState.DOWNLOADING]
            elif filter == "seeding":
                torrents = [t for t in torrents if t.state == TorrentState.UPLOADING]
            elif filter == "paused":
                torrents = [t for t in torrents if t.paused]

        # Sort torrents
        if sort:
            reverse_sort = reverse or False
            if sort == "name":
                torrents.sort(key=lambda t: t.name, reverse=reverse_sort)
            elif sort == "size":
                torrents.sort(key=lambda t: t.size, reverse=reverse_sort)
            elif sort == "progress":
                torrents.sort(key=lambda t: t.progress, reverse=reverse_sort)
            elif sort == "dlspeed":
                torrents.sort(key=lambda t: t.dlspeed, reverse=reverse_sort)
            elif sort == "added_on":
                torrents.sort(key=lambda t: t.added_on, reverse=reverse_sort)

        # Apply offset and limit
        if offset:
            torrents = torrents[offset:]
        if limit:
            torrents = torrents[:limit]

        # Convert to qBittorrent format
        return [t.to_qbittorrent_format() for t in torrents]

    async def delete_torrents(self, hashes: Optional[List[str]], delete_files: bool = False):
        """Delete torrents."""
        if hashes is None:
            # Delete all torrents
            hashes = list(self.torrents.keys())

        for torrent_hash in hashes:
            torrent = self.torrents.get(torrent_hash)
            if not torrent:
                continue

            try:
                # Delete from RealDebrid if it exists
                if torrent.realdebrid_id:
                    await self.realdebrid.delete_torrent(torrent.realdebrid_id)

                # Remove from local storage
                del self.torrents[torrent_hash]

                logger.info(f"Deleted torrent: {torrent.name}")

            except Exception as e:
                logger.error(f"Error deleting torrent {torrent_hash}: {e}")

        # Save updated state
        await self._save_torrents()

    async def pause_torrents(self, hashes: Optional[List[str]]):
        """Pause torrents."""
        if hashes is None:
            hashes = list(self.torrents.keys())

        for torrent_hash in hashes:
            torrent = self.torrents.get(torrent_hash)
            if torrent:
                torrent.paused = True
                if torrent.state == TorrentState.DOWNLOADING:
                    torrent.state = TorrentState.PAUSED_DL
                elif torrent.state == TorrentState.UPLOADING:
                    torrent.state = TorrentState.PAUSED_UP

        await self._save_torrents()

    async def resume_torrents(self, hashes: Optional[List[str]]):
        """Resume torrents."""
        if hashes is None:
            hashes = list(self.torrents.keys())

        for torrent_hash in hashes:
            torrent = self.torrents.get(torrent_hash)
            if torrent:
                torrent.paused = False
                if torrent.state == TorrentState.PAUSED_DL:
                    torrent.state = TorrentState.DOWNLOADING
                elif torrent.state == TorrentState.PAUSED_UP:
                    torrent.state = TorrentState.UPLOADING

        await self._save_torrents()

    async def get_torrent_properties(self, torrent_hash: str) -> Optional[Dict[str, Any]]:
        """Get detailed torrent properties."""
        torrent = self.torrents.get(torrent_hash)
        if not torrent:
            return None

        return {
            "addition_date": int(torrent.added_on.timestamp()),
            "comment": "",
            "completion_date": torrent.completion_on,
            "created_by": "",
            "creation_date": int(torrent.added_on.timestamp()),
            "dl_limit": -1,
            "dl_speed": torrent.dlspeed,
            "dl_speed_avg": torrent.dlspeed,
            "eta": torrent.eta,
            "last_seen": torrent.last_activity,
            "nb_connections": 0,
            "nb_connections_limit": 100,
            "peers": torrent.num_seeds + torrent.num_leechs,
            "peers_total": torrent.num_seeds + torrent.num_leechs,
            "piece_size": 0,
            "pieces_have": int(torrent.progress * 100),
            "pieces_num": 100,
            "reannounce": 0,
            "save_path": torrent.save_path,
            "seeding_time": torrent.seeding_time,
            "seeds": torrent.num_seeds,
            "seeds_total": torrent.num_seeds,
            "share_ratio": torrent.ratio,
            "time_elapsed": torrent.time_active,
            "total_downloaded": torrent.downloaded,
            "total_downloaded_session": torrent.downloaded,
            "total_size": torrent.size,
            "total_uploaded": torrent.uploaded,
            "total_uploaded_session": torrent.uploaded,
            "total_wasted": 0,
            "up_limit": -1,
            "up_speed": torrent.upspeed,
            "up_speed_avg": torrent.upspeed
        }

    async def get_main_data(self, rid: Optional[int] = None) -> Dict[str, Any]:
        """Get main sync data for qBittorrent."""
        current_rid = rid or 0
        self._sync_id += 1

        # Get all torrents in qBittorrent format
        torrents_data = {}
        for torrent_hash, torrent in self.torrents.items():
            torrents_data[torrent_hash] = torrent.to_qbittorrent_format()

        # Build server state
        server_state = {
            "connection_status": "connected",
            "dht_nodes": 0,
            "dl_info_data": sum(t.downloaded for t in self.torrents.values()),
            "dl_info_speed": sum(t.dlspeed for t in self.torrents.values()),
            "dl_rate_limit": 0,
            "up_info_data": sum(t.uploaded for t in self.torrents.values()),
            "up_info_speed": sum(t.upspeed for t in self.torrents.values()),
            "up_rate_limit": 0,
            "queueing": False,
            "use_alt_speed_limits": False,
            "refresh_interval": 1500
        }

        main_data = {
            "rid": self._sync_id,
            "full_update": current_rid == 0,
            "torrents": torrents_data,
            "torrents_removed": [],
            "categories": {},
            "categories_removed": [],
            "tags": [],
            "tags_removed": [],
            "server_state": server_state
        }

        self._last_sync_data = main_data
        return main_data

    async def get_torrent_peers(self, torrent_hash: str, rid: Optional[int] = None) -> Dict[str, Any]:
        """Get torrent peers data."""
        # Since we're using RealDebrid, we don't have real peer data
        return {
            "rid": rid or 0,
            "full_update": True,
            "peers": {},
            "peers_removed": []
        }