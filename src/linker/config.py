"""Configuration management for RealDebrid qBittorrent API Proxy."""

import os
from pathlib import Path
from typing import Optional

import yaml
from pydantic import BaseSettings, <PERSON>
from pydantic_settings import BaseSettings as PydanticBaseSettings


class ServerConfig(BaseSettings):
    """Server configuration."""
    host: str = Field(default="0.0.0.0", env="SERVER_HOST")
    port: int = Field(default=8080, env="SERVER_PORT")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")


class QBittorrentConfig(BaseSettings):
    """qBittorrent API emulation configuration."""
    username: str = Field(default="admin", env="QB_USERNAME")
    password: str = Field(default="adminadmin", env="QB_PASSWORD")
    session_timeout: int = Field(default=3600)


class RealDebridConfig(BaseSettings):
    """RealDebrid API configuration."""
    api_token: str = Field(env="REALDEBRID_API_TOKEN")
    base_url: str = Field(default="https://api.real-debrid.com/rest/1.0")
    poll_interval: int = Field(default=30)
    max_concurrent_torrents: int = Field(default=10)
    preferred_host: str = Field(default="")


class RcloneConfig(BaseSettings):
    """rclone configuration."""
    rc_url: str = Field(default="http://localhost:5572", env="RCLONE_RC_URL")
    remote_name: str = Field(default="realdebrid:", env="RCLONE_REMOTE_NAME")
    mount_path: str = Field(default="/mnt/realdebrid", env="RCLONE_MOUNT_PATH")
    refresh_command: str = Field(default="vfs/refresh")


class StorageConfig(BaseSettings):
    """Storage configuration."""
    data_dir: str = Field(default="./data")
    db_file: str = Field(default="torrents.db")


class LoggingConfig(BaseSettings):
    """Logging configuration."""
    file: str = Field(default="./logs/proxy.log")
    rotation: str = Field(default="10 MB")
    retention: str = Field(default="7 days")
    format: str = Field(
        default="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
    )


class Config(PydanticBaseSettings):
    """Main configuration class."""
    server: ServerConfig = Field(default_factory=ServerConfig)
    qbittorrent: QBittorrentConfig = Field(default_factory=QBittorrentConfig)
    realdebrid: RealDebridConfig = Field(default_factory=RealDebridConfig)
    rclone: RcloneConfig = Field(default_factory=RcloneConfig)
    storage: StorageConfig = Field(default_factory=StorageConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


def load_config(config_file: Optional[str] = None) -> Config:
    """Load configuration from file and environment variables."""
    config_file = config_file or os.getenv("CONFIG_FILE", "config.yaml")
    config_path = Path(config_file)

    # Start with default config
    config_data = {}

    # Load from YAML file if it exists
    if config_path.exists():
        with open(config_path, "r", encoding="utf-8") as f:
            config_data = yaml.safe_load(f) or {}

    # Create config objects with data from file
    server_config = ServerConfig(**config_data.get("server", {}))
    qb_config = QBittorrentConfig(**config_data.get("qbittorrent", {}))
    rd_config = RealDebridConfig(**config_data.get("realdebrid", {}))
    rclone_config = RcloneConfig(**config_data.get("rclone", {}))
    storage_config = StorageConfig(**config_data.get("storage", {}))
    logging_config = LoggingConfig(**config_data.get("logging", {}))

    return Config(
        server=server_config,
        qbittorrent=qb_config,
        realdebrid=rd_config,
        rclone=rclone_config,
        storage=storage_config,
        logging=logging_config,
    )