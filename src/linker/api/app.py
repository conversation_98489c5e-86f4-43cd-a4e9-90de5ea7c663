"""Application API routes."""

from fastapi import APIRouter, Depends
from typing import Dict, Any

from ..app import get_app_state, AppState
from ..auth import require_auth, get_session_id

router = APIRouter()


def create_auth_dependency(app_state: AppState):
    """Create authentication dependency with app state."""
    def auth_dep(session_id: str = Depends(get_session_id)):
        return require_auth(session_id, app_state.auth_manager)
    return auth_dep


@router.get("/version")
async def get_version(
    app_state: AppState = Depends(get_app_state),
    _: str = Depends(lambda: create_auth_dependency(get_app_state())())
):
    """Get application version - emulates qBittorrent version endpoint."""
    return "v4.6.0"  # Emulate a recent qBittorrent version


@router.get("/webapiVersion")
async def get_webapi_version(
    app_state: AppState = Depends(get_app_state),
    _: str = Depends(lambda: create_auth_dependency(get_app_state())())
):
    """Get WebAPI version - emulates qBittorrent WebAPI version endpoint."""
    return "2.8.3"  # Latest qBittorrent WebAPI version


@router.get("/buildInfo")
async def get_build_info(
    app_state: AppState = Depends(get_app_state),
    _: str = Depends(lambda: create_auth_dependency(get_app_state())())
) -> Dict[str, Any]:
    """Get build info - emulates qBittorrent build info endpoint."""
    return {
        "qt": "6.5.0",
        "libtorrent": "2.0.9.0",
        "boost": "1.82.0",
        "openssl": "3.0.8",
        "bitness": 64
    }


@router.get("/preferences")
async def get_preferences(
    app_state: AppState = Depends(get_app_state),
    _: str = Depends(lambda: create_auth_dependency(get_app_state())())
) -> Dict[str, Any]:
    """Get application preferences - emulates qBittorrent preferences endpoint."""
    return {
        "locale": "en",
        "save_path": app_state.config.rclone.mount_path,
        "temp_path_enabled": False,
        "temp_path": "",
        "scan_dirs": {},
        "export_dir": "",
        "export_dir_fin": "",
        "mail_notification_enabled": False,
        "mail_notification_sender": "",
        "mail_notification_email": "",
        "mail_notification_smtp": "",
        "mail_notification_ssl_enabled": False,
        "mail_notification_auth_enabled": False,
        "mail_notification_username": "",
        "mail_notification_password": "",
        "autorun_enabled": False,
        "autorun_program": "",
        "queueing_enabled": False,
        "max_active_downloads": 3,
        "max_active_torrents": 5,
        "max_active_uploads": 3,
        "dont_count_slow_torrents": False,
        "slow_torrent_dl_rate_threshold": 2,
        "slow_torrent_ul_rate_threshold": 2,
        "slow_torrent_inactive_timer": 60,
        "max_ratio_enabled": False,
        "max_ratio": -1,
        "max_ratio_act": 0,
        "listen_port": 8999,
        "upnp": True,
        "random_port": False,
        "dl_limit": 0,
        "up_limit": 0,
        "max_connec": 200,
        "max_connec_per_torrent": 100,
        "max_uploads": 8,
        "max_uploads_per_torrent": 4,
        "stop_tracker_timeout": 1,
        "enable_piece_extent_affinity": False,
        "bittorrent_protocol": 0,
        "limit_utp_rate": True,
        "limit_tcp_overhead": False,
        "limit_lan_peers": True,
        "alt_dl_limit": 10240,
        "alt_up_limit": 10240,
        "scheduler_enabled": False,
        "schedule_from_hour": 8,
        "schedule_from_min": 0,
        "schedule_to_hour": 20,
        "schedule_to_min": 0,
        "scheduler_days": 0,
        "dht": True,
        "pex": True,
        "lsd": True,
        "encryption": 0,
        "anonymous_mode": False,
        "proxy_type": 0,
        "proxy_ip": "0.0.0.0",
        "proxy_port": 8080,
        "proxy_peer_connections": False,
        "proxy_auth_enabled": False,
        "proxy_username": "",
        "proxy_password": "",
        "proxy_torrents_only": False,
        "ip_filter_enabled": False,
        "ip_filter_path": "",
        "ip_filter_trackers": False,
        "web_ui_domain_list": "*",
        "web_ui_address": "*",
        "web_ui_port": app_state.config.server.port,
        "web_ui_upnp": False,
        "web_ui_username": app_state.config.qbittorrent.username,
        "web_ui_password": "",  # Don't expose password
        "web_ui_csrf_protection_enabled": True,
        "web_ui_clickjacking_protection_enabled": True,
        "web_ui_secure_cookie_enabled": False,
        "web_ui_max_auth_fail_count": 5,
        "web_ui_ban_duration": 3600,
        "web_ui_session_timeout": app_state.config.qbittorrent.session_timeout,
        "web_ui_host_header_validation_enabled": True,
        "bypass_local_auth": False,
        "bypass_auth_subnet_whitelist_enabled": False,
        "bypass_auth_subnet_whitelist": "",
        "alternative_webui_enabled": False,
        "alternative_webui_path": "",
        "use_https": False,
        "ssl_key": "",
        "ssl_cert": "",
        "web_ui_https_key_path": "",
        "web_ui_https_cert_path": "",
        "dyndns_enabled": False,
        "dyndns_service": 0,
        "dyndns_username": "",
        "dyndns_password": "",
        "dyndns_domain": "",
        "rss_refresh_interval": 30,
        "rss_max_articles_per_feed": 50,
        "rss_processing_enabled": False,
        "rss_auto_downloading_enabled": False,
        "rss_download_repack_proper_episodes": True,
        "rss_smart_episode_filters": "",
        "add_trackers_enabled": False,
        "add_trackers": "",
        "web_ui_use_custom_http_headers_enabled": False,
        "web_ui_custom_http_headers": "",
        "max_seeding_time_enabled": False,
        "max_seeding_time": -1,
        "announce_ip": "",
        "announce_to_all_tiers": True,
        "announce_to_all_trackers": False,
        "async_io_threads": 10,
        "banned_IPs": "",
        "checking_memory_use": 32,
        "current_interface_address": "",
        "current_network_interface": "",
        "disk_cache": -1,
        "disk_cache_ttl": 60,
        "embedded_tracker_port": 9000,
        "enable_coalesce_read_write": True,
        "enable_embedded_tracker": False,
        "enable_multi_connections_from_same_ip": False,
        "enable_os_cache": True,
        "enable_upload_suggestions": False,
        "file_pool_size": 40,
        "outgoing_ports_max": 0,
        "outgoing_ports_min": 0,
        "recheck_completed_torrents": False,
        "resolve_peer_countries": True,
        "save_resume_data_interval": 60,
        "send_buffer_low_watermark": 10,
        "send_buffer_watermark": 500,
        "send_buffer_watermark_factor": 50,
        "socket_backlog_size": 30,
        "upload_choking_algorithm": 1,
        "upload_slots_behavior": 0,
        "upnp_lease_duration": 0,
        "utp_tcp_mixed_mode": 0
    }