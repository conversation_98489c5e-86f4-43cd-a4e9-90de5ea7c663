"""Authentication API routes."""

from fastapi import APIRouter, Form, Response, Depends, HTTPException, status
from loguru import logger

from ..app import get_app_state, AppState
from ..auth import get_session_id

router = APIRouter()


@router.post("/login")
async def login(
    response: Response,
    username: str = Form(...),
    password: str = Form(...),
    app_state: AppState = Depends(get_app_state)
):
    """Login endpoint - emulates qBittorrent login."""
    try:
        session_id = app_state.auth_manager.authenticate(username, password)

        # Set session cookie
        response.set_cookie(
            key="SID",
            value=session_id,
            path="/",
            httponly=True,
            secure=False,  # Set to True in production with HTTPS
            samesite="lax"
        )

        return "Ok."

    except HTTPException as e:
        if e.status_code == status.HTTP_403_FORBIDDEN:
            # qBittorrent returns 403 for invalid credentials
            raise HTTPException(status_code=403, detail="Fails.")
        raise


@router.post("/logout")
async def logout(
    response: Response,
    session_id: str = Depends(get_session_id),
    app_state: AppState = Depends(get_app_state)
):
    """Logout endpoint - emulates qBittorrent logout."""
    app_state.auth_manager.logout(session_id)

    # Clear session cookie
    response.delete_cookie(key="SID", path="/")

    return "Ok."