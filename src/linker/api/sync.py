"""Sync API routes."""

from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, Query
from loguru import logger

from ..app import get_app_state, AppState
from ..auth import get_session_id

router = APIRouter()


def create_auth_dependency(app_state: AppState):
    """Create authentication dependency with app state."""
    def auth_dep(session_id: str = Depends(get_session_id)):
        from ..auth import require_auth
        return require_auth(session_id, app_state.auth_manager)
    return auth_dep


@router.get("/maindata")
async def get_main_data(
    rid: Optional[int] = Query(None),
    app_state: AppState = Depends(get_app_state),
    _: str = Depends(lambda: create_auth_dependency(get_app_state())())
) -> Dict[str, Any]:
    """Get main data - emulates qBittorrent sync/maindata endpoint."""
    try:
        main_data = await app_state.torrent_manager.get_main_data(rid)
        return main_data
    except Exception as e:
        logger.error(f"Error getting main data: {e}")
        return {
            "rid": rid or 0,
            "full_update": True,
            "torrents": {},
            "torrents_removed": [],
            "categories": {},
            "categories_removed": [],
            "tags": [],
            "tags_removed": [],
            "server_state": {
                "connection_status": "connected",
                "dht_nodes": 0,
                "dl_info_data": 0,
                "dl_info_speed": 0,
                "dl_rate_limit": 0,
                "up_info_data": 0,
                "up_info_speed": 0,
                "up_rate_limit": 0,
                "queueing": False,
                "use_alt_speed_limits": False,
                "refresh_interval": 1500
            }
        }


@router.get("/torrentPeers")
async def get_torrent_peers(
    hash: str,
    rid: Optional[int] = Query(None),
    app_state: AppState = Depends(get_app_state),
    _: str = Depends(lambda: create_auth_dependency(get_app_state())())
) -> Dict[str, Any]:
    """Get torrent peers - emulates qBittorrent sync/torrentPeers endpoint."""
    try:
        peers_data = await app_state.torrent_manager.get_torrent_peers(hash, rid)
        return peers_data
    except Exception as e:
        logger.error(f"Error getting torrent peers: {e}")
        return {
            "rid": rid or 0,
            "full_update": True,
            "peers": {},
            "peers_removed": []
        }