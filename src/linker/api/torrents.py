"""Torrents API routes."""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, Form, File, UploadFile, HTTPException, status
from loguru import logger

from ..app import get_app_state, AppState
from ..auth import get_session_id
from ..models import TorrentInfo

router = APIRouter()


def create_auth_dependency(app_state: AppState):
    """Create authentication dependency with app state."""
    def auth_dep(session_id: str = Depends(get_session_id)):
        from ..auth import require_auth
        return require_auth(session_id, app_state.auth_manager)
    return auth_dep


@router.get("/info")
async def get_torrents_info(
    filter: Optional[str] = None,
    category: Optional[str] = None,
    tag: Optional[str] = None,
    sort: Optional[str] = None,
    reverse: Optional[bool] = None,
    limit: Optional[int] = None,
    offset: Optional[int] = None,
    hashes: Optional[str] = None,
    app_state: AppState = Depends(get_app_state),
    _: str = Depends(lambda: create_auth_dependency(get_app_state())())
) -> List[Dict[str, Any]]:
    """Get torrents info - emulates qBittorrent torrents/info endpoint."""
    try:
        torrents = await app_state.torrent_manager.get_torrents_info(
            filter=filter,
            category=category,
            tag=tag,
            sort=sort,
            reverse=reverse,
            limit=limit,
            offset=offset,
            hashes=hashes
        )
        return torrents
    except Exception as e:
        logger.error(f"Error getting torrents info: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/add")
async def add_torrent(
    urls: Optional[str] = Form(None),
    torrents: Optional[List[UploadFile]] = File(None),
    savepath: Optional[str] = Form(None),
    cookie: Optional[str] = Form(None),
    category: Optional[str] = Form(None),
    tags: Optional[str] = Form(None),
    skip_checking: Optional[str] = Form(None),
    paused: Optional[str] = Form(None),
    root_folder: Optional[str] = Form(None),
    rename: Optional[str] = Form(None),
    upLimit: Optional[int] = Form(None),
    dlLimit: Optional[int] = Form(None),
    ratioLimit: Optional[float] = Form(None),
    seedingTimeLimit: Optional[int] = Form(None),
    autoTMM: Optional[str] = Form(None),
    sequentialDownload: Optional[str] = Form(None),
    firstLastPiecePrio: Optional[str] = Form(None),
    app_state: AppState = Depends(get_app_state),
    _: str = Depends(lambda: create_auth_dependency(get_app_state())())
):
    """Add torrent - emulates qBittorrent torrents/add endpoint."""
    try:
        # Handle magnet links
        if urls:
            magnet_links = [url.strip() for url in urls.split('\n') if url.strip()]
            for magnet_link in magnet_links:
                if magnet_link.startswith('magnet:'):
                    await app_state.torrent_manager.add_magnet(
                        magnet_link,
                        category=category,
                        tags=tags,
                        paused=paused == 'true'
                    )
                else:
                    logger.warning(f"Unsupported URL format: {magnet_link}")

        # Handle torrent files
        if torrents:
            for torrent_file in torrents:
                if torrent_file.filename and torrent_file.filename.endswith('.torrent'):
                    content = await torrent_file.read()
                    await app_state.torrent_manager.add_torrent_file(
                        content,
                        filename=torrent_file.filename,
                        category=category,
                        tags=tags,
                        paused=paused == 'true'
                    )

        return "Ok."

    except Exception as e:
        logger.error(f"Error adding torrent: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/delete")
async def delete_torrents(
    hashes: str = Form(...),
    deleteFiles: Optional[str] = Form("false"),
    app_state: AppState = Depends(get_app_state),
    _: str = Depends(lambda: create_auth_dependency(get_app_state())())
):
    """Delete torrents - emulates qBittorrent torrents/delete endpoint."""
    try:
        hash_list = hashes.split('|') if hashes != "all" else None
        delete_files = deleteFiles.lower() == "true"

        await app_state.torrent_manager.delete_torrents(hash_list, delete_files)
        return "Ok."

    except Exception as e:
        logger.error(f"Error deleting torrents: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/pause")
async def pause_torrents(
    hashes: str = Form(...),
    app_state: AppState = Depends(get_app_state),
    _: str = Depends(lambda: create_auth_dependency(get_app_state())())
):
    """Pause torrents - emulates qBittorrent torrents/pause endpoint."""
    try:
        hash_list = hashes.split('|') if hashes != "all" else None
        await app_state.torrent_manager.pause_torrents(hash_list)
        return "Ok."

    except Exception as e:
        logger.error(f"Error pausing torrents: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/resume")
async def resume_torrents(
    hashes: str = Form(...),
    app_state: AppState = Depends(get_app_state),
    _: str = Depends(lambda: create_auth_dependency(get_app_state())())
):
    """Resume torrents - emulates qBittorrent torrents/resume endpoint."""
    try:
        hash_list = hashes.split('|') if hashes != "all" else None
        await app_state.torrent_manager.resume_torrents(hash_list)
        return "Ok."

    except Exception as e:
        logger.error(f"Error resuming torrents: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/properties")
async def get_torrent_properties(
    hash: str,
    app_state: AppState = Depends(get_app_state),
    _: str = Depends(lambda: create_auth_dependency(get_app_state())())
) -> Dict[str, Any]:
    """Get torrent properties - emulates qBittorrent torrents/properties endpoint."""
    try:
        properties = await app_state.torrent_manager.get_torrent_properties(hash)
        if not properties:
            raise HTTPException(status_code=404, detail="Torrent not found")
        return properties

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting torrent properties: {e}")
        raise HTTPException(status_code=500, detail=str(e))