# RealDebrid qBittorrent API Proxy Environment Variables

# RealDebrid API Token (Required)
# Get this from https://real-debrid.com/apitoken
REALDEBRID_API_TOKEN=your_realdebrid_api_token_here

# qBittorrent API Credentials
# These are the credentials <PERSON><PERSON><PERSON> will use to connect
QB_USERNAME=admin
QB_PASSWORD=adminadmin

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
LOG_LEVEL=INFO

# rclone Configuration
RCLONE_RC_URL=http://localhost:5572
RCLONE_REMOTE_NAME=realdebrid:
RCLONE_MOUNT_PATH=/mnt/realdebrid

# Optional: Override config file path
CONFIG_FILE=config.yaml