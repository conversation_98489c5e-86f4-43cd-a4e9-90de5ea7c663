# RealDebrid qBittorrent API Proxy Configuration

# Server Configuration
server:
  host: "0.0.0.0"
  port: 8080
  log_level: "INFO"

# qBittorrent API Emulation
qbittorrent:
  # Authentication credentials for Sonarr
  username: "admin"
  password: "adminadmin"
  # Session timeout in seconds
  session_timeout: 3600

# RealDebrid Configuration
realdebrid:
  # Your RealDebrid API token
  api_token: ""
  # Base URL for RealDebrid API
  base_url: "https://api.real-debrid.com/rest/1.0"
  # Polling interval for torrent status updates (seconds)
  poll_interval: 30
  # Maximum concurrent torrents
  max_concurrent_torrents: 10
  # Preferred host for torrent uploads (leave empty for auto-selection)
  preferred_host: ""

# rclone Configuration
rclone:
  # rclone remote control endpoint
  rc_url: "http://localhost:5572"
  # Remote name for RealDebrid mount
  remote_name: "realdebrid:"
  # Mount path where files should be available
  mount_path: "/mnt/realdebrid"
  # VFS cache refresh command
  refresh_command: "vfs/refresh"

# Storage Configuration
storage:
  # Directory to store torrent state database
  data_dir: "./data"
  # Database file name
  db_file: "torrents.db"

# Logging Configuration
logging:
  # Log file path
  file: "./logs/proxy.log"
  # Log rotation settings
  rotation: "10 MB"
  retention: "7 days"
  # Log format
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"